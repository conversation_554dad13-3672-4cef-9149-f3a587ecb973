import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/types/database.types';
import { FieldReportFormData } from '@/utils/fieldReportValidation';
import { useDataFiltering } from '@/utils/dataFiltering';
import { useAuth } from '@/hooks/useAuth';

type FieldReport = Database['public']['Tables']['field_reports']['Row'] & {
  staff_name: string;
  school_name: string;
  attendance_check_in_time: string;
  attendance_check_out_time: string;
};

interface UseFieldReportsOptions {
  staffId?: string;
  schoolId?: string;
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
}

export const useFieldReports = (options: UseFieldReportsOptions = {}) => {
  const { user, profile } = useAuth();
  const { filterFieldReports } = useDataFiltering();

  return useQuery({
    queryKey: ['field-reports', options, user?.id, profile?.role],
    queryFn: async (): Promise<FieldReport[]> => {
      // For field staff, use direct query to avoid RPC authentication issues
      if (profile?.role === 'field_staff') {
        let query = supabase
          .from('enhanced_field_reports')
          .select('*')
          .eq('staff_id', user?.id)
          .order('report_date', { ascending: false });

        // Apply additional filters for field staff
        if (options.schoolId) {
          query = query.eq('school_id', options.schoolId);
        }

        if (options.dateFrom) {
          query = query.gte('report_date', options.dateFrom);
        }

        if (options.dateTo) {
          query = query.lte('report_date', options.dateTo);
        }

        if (options.limit) {
          query = query.limit(options.limit);
        }

        const { data, error } = await query;

        if (error) {
          throw new Error(`Failed to fetch field reports: ${error.message}`);
        }

        return (data || []).map(report => ({
          ...report,
          // Ensure compatibility with existing interface
          attendance_check_in_time: report.check_in_time || '',
          attendance_check_out_time: report.check_out_time || '',
        }));
      }

      // For admin and program officers, try the RPC function first
      try {
        const filterResult = filterFieldReports(options.staffId, {
          schoolId: options.schoolId,
          dateFrom: options.dateFrom,
          dateTo: options.dateTo,
          limit: options.limit,
        });

        if (!filterResult.hasAccess || !filterResult.query) {
          console.warn('Access denied to field reports:', filterResult.reason);
          return [];
        }

        const { data, error } = await filterResult.query;

        if (error) {
          console.warn('RPC function failed, falling back to direct query:', error.message);
          throw error; // This will trigger the fallback
        }

        // RPC function already includes staff_name, school_name, and attendance times
        return (data || []).map(report => ({
          ...report,
          // Ensure compatibility with existing interface
          attendance_check_in_time: report.check_in_time || '',
          attendance_check_out_time: report.check_out_time || '',
        }));
      } catch (error) {
        console.warn('Filtering failed, using fallback query:', error);

        // Fallback to original query logic for admin/program officers
        let query = supabase
          .from('enhanced_field_reports')
          .select('*')
          .order('report_date', { ascending: false });

        // Apply role-based filtering manually for fallback
        if (options.staffId) {
          query = query.eq('staff_id', options.staffId);
        }

        // Apply additional filters
        if (options.schoolId) {
          query = query.eq('school_id', options.schoolId);
        }

        if (options.dateFrom) {
          query = query.gte('report_date', options.dateFrom);
        }

        if (options.dateTo) {
          query = query.lte('report_date', options.dateTo);
        }

        if (options.limit) {
          query = query.limit(options.limit);
        }

        const { data, error: fallbackError } = await query;

        if (fallbackError) {
          throw new Error(`Failed to fetch field reports: ${fallbackError.message}`);
        }

        // Enhanced view already includes staff_name, school_name, and attendance times
        return (data || []).map(report => ({
          ...report,
          // Ensure compatibility with existing interface
          attendance_check_in_time: report.check_in_time || '',
          attendance_check_out_time: report.check_out_time || '',
        }));
      }
    },
    enabled: !!user && !!profile?.role,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useFieldReport = (reportId: string) => {
  const { user, profile } = useAuth();
  const { filterFieldReports } = useDataFiltering();

  return useQuery({
    queryKey: ['field-report', reportId, user?.id, profile?.role],
    queryFn: async (): Promise<FieldReport> => {
      // First get the report to check ownership
      const { data: reportData, error: reportError } = await supabase
        .from('enhanced_field_reports')
        .select('*')
        .eq('id', reportId)
        .single();

      if (reportError) {
        throw new Error(`Failed to fetch field report: ${reportError.message}`);
      }

      if (!reportData) {
        throw new Error('Field report not found');
      }

      // Check access based on the report's staff_id
      const filterResult = filterFieldReports(reportData.staff_id);

      if (!filterResult.hasAccess) {
        throw new Error(`Access denied: ${filterResult.reason}`);
      }

      // Enhanced view already includes staff_name, school_name, and attendance times
      return {
        ...reportData,
        // Ensure compatibility with existing interface
        attendance_check_in_time: reportData.check_in_time || '',
        attendance_check_out_time: reportData.check_out_time || '',
      };
    },
    enabled: !!reportId && !!user && !!profile?.role,
  });
};

// Hook for updating a field report
export const useUpdateFieldReport = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ reportId, data }: { reportId: string; data: FieldReportFormData }) => {
      const { error } = await supabase
        .from('field_reports')
        .update({
          activity_type: data.activity_type,
          // Map form field names to database field names
          round_table_sessions: data.round_table_sessions_count,
          total_students: data.total_students_attended,
          students_per_session: data.students_per_session,
          activities_conducted: data.activities_conducted,
          topics_covered: data.topics_covered,
          challenges: data.challenges_encountered,
          wins: data.wins_achieved,
          observations: data.general_observations,
          lessons_learned: data.lessons_learned,
          follow_up_required: data.follow_up_required,
          follow_up_actions: data.follow_up_actions,
          introduction: data.introduction,
          recommendations: data.recommendations,
          updated_at: new Date().toISOString(),
        })
        .eq('id', reportId);

      if (error) {
        throw new Error(`Failed to update field report: ${error.message}`);
      }

      return { success: true };
    },
    onSuccess: () => {
      // Invalidate and refetch field reports
      queryClient.invalidateQueries({ queryKey: ['field-reports'] });
      queryClient.invalidateQueries({ queryKey: ['field-report'] });
    },
  });
};

// Hook for deleting a field report
export const useDeleteFieldReport = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (reportId: string) => {
      const { error } = await supabase
        .from('field_reports')
        .delete()
        .eq('id', reportId);

      if (error) {
        throw new Error(`Failed to delete field report: ${error.message}`);
      }

      return { success: true };
    },
    onSuccess: () => {
      // Invalidate and refetch field reports
      queryClient.invalidateQueries({ queryKey: ['field-reports'] });
      queryClient.invalidateQueries({ queryKey: ['field-report'] });
    },
  });
};
