import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  User, 
  MapPin, 
  Calendar, 
  Clock,
  Users,
  Target,
  BookOpen,
  AlertTriangle,
  Trophy,
  Eye,
  CheckCircle,
  XCircle,
  Edit,
  Send,
  ArrowLeft
} from 'lucide-react';
import { format } from 'date-fns';
import { FieldReportFormData } from '@/utils/fieldReportValidation';
import { getLessonById, ALL_LESSON_TOPICS } from '@/constants/lessonTopics';

interface FieldReportPreviewProps {
  reportData: FieldReportFormData;
  schoolName?: string;
  staffName?: string;
  onEdit: () => void;
  onSubmit: () => void;
  isSubmitting?: boolean;
}

const FieldReportPreview: React.FC<FieldReportPreviewProps> = ({
  reportData,
  schoolName = 'Current School',
  staffName = 'Current User',
  onEdit,
  onSubmit,
  isSubmitting = false
}) => {
  const formatActivityType = (activityType: string) => {
    return activityType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const getActivityTypeBadgeColor = (activityType: string) => {
    switch (activityType) {
      case 'round_table_session':
        return 'bg-blue-100 text-blue-800';
      case 'school_visit':
        return 'bg-green-100 text-green-800';
      case 'meeting':
        return 'bg-purple-100 text-purple-800';
      case 'assessment':
        return 'bg-yellow-100 text-yellow-800';
      case 'other':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Helper function to format lesson topics with category
  const formatLessonTopic = (topic: string) => {
    // First try to find by ID
    const lesson = getLessonById(topic);
    if (lesson) {
      return `${lesson.category}: ${lesson.title}`;
    }

    // If not found by ID, try to find by title
    const lessonByTitle = ALL_LESSON_TOPICS.find(l => l.title === topic);
    if (lessonByTitle) {
      return `${lessonByTitle.category}: ${lessonByTitle.title}`;
    }

    // If still not found, return as is (might be a custom topic)
    return topic;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Field Report Preview
              </CardTitle>
              <CardDescription>
                Review your report before submitting
              </CardDescription>
            </div>
            <Badge className={getActivityTypeBadgeColor(reportData.activity_type)}>
              {formatActivityType(reportData.activity_type)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="font-medium">Staff Member</div>
                  <div className="text-sm text-gray-600">{staffName}</div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="font-medium">School</div>
                  <div className="text-sm text-gray-600">{schoolName}</div>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="font-medium">Report Date</div>
                  <div className="text-sm text-gray-600">
                    {format(new Date(), 'EEEE, MMMM dd, yyyy')}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                {reportData.follow_up_required ? (
                  <CheckCircle className="h-5 w-5 text-orange-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-green-500" />
                )}
                <div>
                  <div className="font-medium">Follow-up Required</div>
                  <div className="text-sm text-gray-600">
                    {reportData.follow_up_required ? 'Yes' : 'No'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Session Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Session Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {reportData.total_round_tables_calculated || reportData.round_table_sessions || 0}
              </div>
              <div className="text-sm text-gray-600">Round Table Sessions</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {reportData.total_students_calculated || reportData.total_students || 0}
              </div>
              <div className="text-sm text-gray-600">Total Students</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">
                {reportData.students_per_session || 0}
              </div>
              <div className="text-sm text-gray-600">Students per Session</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Activities and Topics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Activities Conducted
            </CardTitle>
          </CardHeader>
          <CardContent>
            {reportData.activities_conducted && reportData.activities_conducted.length > 0 ? (
              <ul className="space-y-2">
                {reportData.activities_conducted.map((activity, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-sm">{activity}</span>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500">No activities recorded</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Lesson/Topic Covered
            </CardTitle>
          </CardHeader>
          <CardContent>
            {reportData.topics_covered && reportData.topics_covered.length > 0 ? (
              <ul className="space-y-3">
                {reportData.topics_covered.map((topic, index) => {
                  const formattedTopic = formatLessonTopic(topic);
                  const [category, lesson] = formattedTopic.includes(':')
                    ? formattedTopic.split(': ', 2)
                    : ['', formattedTopic];

                  return (
                    <li key={index} className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                      <div className="flex-1">
                        {category && (
                          <Badge variant="outline" className="text-xs mb-1 mr-2">
                            {category}
                          </Badge>
                        )}
                        <span className="text-sm">{lesson || topic}</span>
                      </div>
                    </li>
                  );
                })}
              </ul>
            ) : (
              <p className="text-sm text-gray-500">No lessons/topics recorded</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Challenges and Wins */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Challenges Encountered
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm whitespace-pre-wrap">
              {reportData.challenges_encountered || 'No challenges reported'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              Wins Achieved
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm whitespace-pre-wrap">
              {reportData.wins_achieved || 'No wins reported'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* General Observations and Lessons Learned */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              General Observations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm whitespace-pre-wrap">
              {reportData.general_observations || 'No observations recorded'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Lessons Learned
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm whitespace-pre-wrap">
              {reportData.lessons_learned || 'No lessons recorded'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Follow-up Actions */}
      {reportData.follow_up_required && reportData.follow_up_actions && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-orange-500" />
              Follow-up Actions Required
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm whitespace-pre-wrap">
              {reportData.follow_up_actions}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex gap-4 pt-4">
        <Button
          variant="outline"
          onClick={onEdit}
          className="flex-1"
          disabled={isSubmitting}
        >
          <Edit className="h-4 w-4 mr-2" />
          Edit Report
        </Button>
        <Button
          onClick={onSubmit}
          className="flex-1"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Clock className="h-4 w-4 mr-2 animate-spin" />
              Submitting...
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" />
              Submit Report & Check Out
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default FieldReportPreview;
